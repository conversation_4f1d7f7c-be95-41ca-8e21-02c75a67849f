classdef IMRFileGenerator
    % IMRFileGenerator 用于将原始数据（.csv文件）按照特定协议封装为IMR文件
    %
    % 该类用于读取CSV格式的原始数据，检查数据质量，并将其按照特定协议封装为IMR文件。
    % 支持数据质量检查，包括GNSS周内秒数据的时间间隔检查和丢帧检测。
    %
    % 属性：
    %   - csvFilePath: CSV文件路径
    %   - csvFileCreationTime: CSV文件创建时间
    %   - imrFilePath: IMR文件路径
    %   - frameHeader: 帧头结构（256字节）
    %   - rawDataFrames: 字符串形式的数据
    %   - dataFrames: 数据帧结构（多个帧）
    %   - numFrames: 帧的数量
    %   - bIsIntelOrMotorola: 字节序（0=Intel小端，1=Motorola大端）
    %   - dVersionNumber: 版本号
    %   - bDeltaTheta: 数据类型标志（0=角速率，1=角增量）
    %   - bDeltaVelocity: 数据类型标志（0=加速度，1=速度增量）
    %   - iUtcOrGpsTime: 时间类型（0=未知，1=UTC，2=GPS）
    %   - szImuName: IMU名称
    %   - szProgramName: 程序名称
    %   - dGyroScaleFactor: 陀螺仪比例因子
    %   - dAccelScaleFactor: 加速度计比例因子
    %   - gn: 重力加速度值
    %
    % 方法：
    %   - IMRFileGenerator: 构造函数，初始化对象
    %   - readCSV: 读取CSV文件数据
    %   - generateFrameHeader: 生成帧头
    %   - generateDataFrames: 生成数据帧
    %   - processFrameData: 处理单个帧的数据
    %   - writeIMRFile: 将帧头和数据帧写入IMR文件
    %   - generateIMR: 生成IMR文件的完整流程
    %   - checkGnssTimeInterval: 检查GNSS周内秒数据的时间间隔，检测丢帧
    %   - plotGnssTimeIntervals: 绘制GNSS周内秒数据的时间间隔图和丢帧分析
    %
    % 示例：
    %   % 创建对象
    %   obj = IMRFileGenerator('data.csv', 'output.imr');
    %
    %   % 检查数据质量
    %   [isValid, details] = obj.checkGnssTimeInterval();
    %
    %   % 显示数据质量可视化图表
    %   obj.plotGnssTimeIntervals(details);
    %
    %   % 生成IMR文件（并显示可视化图表）
    %   obj.generateIMR(true);
    %
    % 注意事项：
    %   - CSV文件的第一列应为GNSS周内秒数据
    %   - 检测到大量丢帧时，可能会中止IMR文件生成
    %   - 可视化图表可帮助分析数据质量问题
    %   - MD01第二版结构安装参数
    %   -   GNSS主天线的杆臂参数为：右前上 -0.00454, -0.74840, -0.05050
    %   - 
    %
    % 版本：
    %   - 1.0.0 (2025-03-06): 初始版本
    %   - 1.1.0 (2025-03-10): 添加数据质量检查和丢帧检测功能
    %
    % 作者：
    %   - 利俊纬 <<EMAIL>>
    %
    % 版权：
    %   - Copyright (c) 2025 你的名字或公司名称
    %
    % 许可证：
    %   - MIT License 或其他许可证信息

    properties
        csvFilePath % CSV文件路径
        csvFileCreationTime % CSV文件创建时间
        imrFilePath % IMR文件路径
        frameHeader % 帧头结构（256字节）
        rawDataFrames % 字符串形式的数据dGyroScaleFactor
        dataFrames % 数据帧结构（多个帧）
        numFrames % 帧的数量

        bIsIntelOrMotorola = 0
        dVersionNumber  = 8.80
        bDeltaTheta     = 0
        bDeltaVelocity  = 0
        iUtcOrGpsTime   = 2
        szImuName       = 'INS370-25J'
        szProgramName   = 'MD01'

        dGyroScaleFactor = 1e-7;
        dAccelScaleFactor= 1e-7;
        gn = 9.78815815671269;
    end

    methods
        function obj = IMRFileGenerator(inputSource, imrFilePath)
            % 构造函数
            % 输入:
            %   inputSource - 可以是CSV文件路径，或ComNavResult对象
            %   imrFilePath - IMR文件路径
            % 功能:
            %   1. 如果输入为CSV文件路径，按原逻辑处理
            %   2. 如果输入为ComNavResult对象，从对象属性提取数据

            if ischar(inputSource) || isstring(inputSource)
                csvFilePath = inputSource;
                % 检查CSV文件是否存在
                if exist(csvFilePath, 'file') ~= 2
                    error('CSV文件不存在: %s', csvFilePath);
                end

                % 获取CSV文件的创建时间
                fileInfo = dir(csvFilePath);
                fileCreationTimeStr = fileInfo.date; % 文件创建时间的字符串

                % 将文件创建时间转换为C的time_type（Unix时间戳）
                fileCreationTime = datetime(fileCreationTimeStr, 'InputFormat', 'dd-MMM-yyyy HH:mm:ss'); % 转换为datetime
                unixTime = posixtime(fileCreationTime); % 转换为Unix时间戳

                % 保存Unix时间戳到对象的属性中
                obj.csvFileCreationTime = unixTime; % 假设C的time_type是double类型

                % 初始化其他属性
                obj.csvFilePath = csvFilePath;
                obj.imrFilePath = imrFilePath;
                obj.numFrames = 0; % 初始化帧数量
                % 只在构造时读取CSV
                obj = obj.readCSV();
            elseif isa(inputSource, 'ComNavResult')
                % 通过组合导航类初始化
                navObj = inputSource;
                obj.csvFilePath = '';
                obj.imrFilePath = imrFilePath;
                obj.numFrames = size(navObj.gyro, 1);
                obj.csvFileCreationTime = posixtime(datetime('now'));
                % 构造rawDataFrames: [gnssTow, gyroX, gyroY, gyroZ, accX, accY, accZ]
                if isempty(navObj.gnssTow)
                    error('ComNavResult对象缺少gnssTow数据');
                end
                if isempty(navObj.gyro) || isempty(navObj.accel)
                    error('ComNavResult对象缺少gyro或accel数据');
                end
                gyro = navObj.gyro;
                accel = navObj.accel;
                [gyro, accel] = obj.adjustImuAxisOrder(gyro, accel);
                obj.rawDataFrames = [navObj.gnssTow(:), gyro, accel.*obj.gn];
            else
                error('输入类型不支持，必须为CSV文件路径或ComNavResult对象');
            end
        end

        function [isValid, details] = checkGnssTimeInterval(obj)
            % 检查CSV文件中的GNSS周内秒数据是否按照固定间隔增加
            % 检测并修复周内秒为0的异常点
            if isempty(obj.rawDataFrames) || obj.numFrames == 0
                error('数据未初始化，请通过构造函数初始化对象');
            end

            gnssTimeData = obj.rawDataFrames(:, 1);

            % 检查是否存在周内秒为0的点
            zeroIdx = find(gnssTimeData == 0);
            if ~isempty(zeroIdx)
                warning('检测到%d个周内秒为0的异常点，自动修复中...', numel(zeroIdx));
                % 估算采样间隔
                if obj.numFrames > 2
                    dt = median(diff(gnssTimeData(gnssTimeData > 0)));
                else
                    dt = 0.005; % 默认值
                end
                for i = 1:length(zeroIdx)
                    idx = zeroIdx(i);
                    % 找到上一个非零点
                    prevIdx = find(gnssTimeData(1:idx-1) > 0, 1, 'last');
                    if isempty(prevIdx)
                        % 若前面全为0，则用下一个非零点反推
                        nextIdx = find(gnssTimeData(idx+1:end) > 0, 1, 'first');
                        if ~isempty(nextIdx)
                            gnssTimeData(idx) = gnssTimeData(idx+nextIdx) - dt * nextIdx;
                        else
                            gnssTimeData(idx) = dt * idx; % 全部为0时兜底
                        end
                    else
                        gnssTimeData(idx) = gnssTimeData(prevIdx) + dt * (idx - prevIdx);
                    end
                end
                % 修复后写回
                obj.rawDataFrames(:, 1) = gnssTimeData;
            end

            % 计算时间间隔
            timeIntervals = diff(gnssTimeData);

            % 计算预期的时间间隔（使用众数作为预期间隔）
            [counts, values] = hist(timeIntervals, unique(timeIntervals));
            [~, maxIdx] = max(counts);
            expectedInterval = values(maxIdx);

            % 找出异常点（与预期间隔相差超过1%的点）
            tolerance = 0.01 * expectedInterval; % 1%的容差
            anomalyIndices = find(abs(timeIntervals - expectedInterval) > tolerance);
            anomalyValues = timeIntervals(anomalyIndices);

            % 计算统计信息
            stats.min = min(timeIntervals);
            stats.max = max(timeIntervals);
            stats.mean = mean(timeIntervals);
            stats.std = std(timeIntervals);
            stats.median = median(timeIntervals);

            % 判断数据是否有效（没有异常点）
            isValid = isempty(anomalyIndices);

            % 计算丢帧信息
            droppedFrames = struct('count', 0, 'locations', [], 'framesPerDrop', []);

            if ~isempty(anomalyIndices)
                % 对于每个异常点，计算丢失的帧数
                droppedFramesLocations = anomalyIndices; % 丢帧位置（索引）
                droppedFramesCount = zeros(size(droppedFramesLocations)); % 每个位置丢失的帧数

                for i = 1:length(anomalyIndices)
                    idx = anomalyIndices(i);
                    actualInterval = timeIntervals(idx);

                    % 计算丢失的帧数（向下取整，因为可能有小的时间误差）
                    lostFrames = round(actualInterval / expectedInterval) - 1;

                    % 确保丢失的帧数至少为1
                    if lostFrames < 1
                        % 如果间隔明显大于预期但计算出的丢帧数小于1，则认为至少丢失了1帧
                        if actualInterval > (1.5 * expectedInterval)
                            lostFrames = 1;
                        else
                            lostFrames = 0; % 可能是其他类型的异常，不是丢帧
                        end
                    end

                    droppedFramesCount(i) = lostFrames;
                end

                % 过滤掉不是由丢帧引起的异常（丢帧数为0的点）
                validDrops = droppedFramesCount > 0;
                droppedFramesLocations = droppedFramesLocations(validDrops);
                droppedFramesCount = droppedFramesCount(validDrops);

                % 计算总丢帧数
                totalDroppedFrames = sum(droppedFramesCount);

                % 保存丢帧信息
                droppedFrames.count = totalDroppedFrames;
                droppedFrames.locations = droppedFramesLocations;
                droppedFrames.framesPerDrop = droppedFramesCount;
            end

            % 构建详细信息结构体
            details.expectedInterval = expectedInterval;
            details.actualIntervals = timeIntervals;
            details.anomalies.indices = anomalyIndices;
            details.anomalies.values = anomalyValues;
            details.statistics = stats;
            details.gnssTimeData = gnssTimeData;
            details.droppedFrames = droppedFrames;

            % 输出检查结果
            if isValid
                disp('GNSS周内秒数据检查通过：时间间隔一致');
                disp(['预期时间间隔: ', num2str(expectedInterval), ' 秒']);
                disp('未检测到丢帧');
            else
                disp('GNSS周内秒数据检查失败：发现异常时间间隔');
                disp(['预期时间间隔: ', num2str(expectedInterval), ' 秒']);
                disp(['发现 ', num2str(length(anomalyIndices)), ' 个异常点']);
                disp(['时间间隔范围: ', num2str(stats.min), ' 到 ', num2str(stats.max), ' 秒']);

                % 显示丢帧信息
                if droppedFrames.count > 0
                    disp(['检测到总丢帧数: ', num2str(droppedFrames.count), ' 帧']);
                    disp(['丢帧发生次数: ', num2str(length(droppedFrames.locations)), ' 次']);

                    % 如果丢帧次数不多，显示详细信息
                    if length(droppedFrames.locations) <= 10
                        disp('丢帧详情:');
                        for i = 1:length(droppedFrames.locations)
                            idx = droppedFrames.locations(i);
                            lostFrames = droppedFrames.framesPerDrop(i);
                            disp(['  索引 ', num2str(idx), ': 丢失 ', num2str(lostFrames), ' 帧, ', ...
                                  '时间从 ', num2str(gnssTimeData(idx)), ' 到 ', ...
                                  num2str(gnssTimeData(idx+1)), ', 间隔: ', ...
                                  num2str(timeIntervals(idx)), ' 秒']);
                        end
                    end
                else
                    disp('未检测到丢帧，异常可能由其他原因引起');
                end

                % 如果异常点不多且不全是丢帧，显示其他异常点详情
                nonDropIndices = setdiff(anomalyIndices, droppedFrames.locations);
                if ~isempty(nonDropIndices) && length(nonDropIndices) <= 10
                    disp('其他异常点详情（非丢帧）:');
                    for i = 1:length(nonDropIndices)
                        idx = nonDropIndices(i);
                        disp(['  索引 ', num2str(idx), ': ', num2str(gnssTimeData(idx)), ' -> ', ...
                              num2str(gnssTimeData(idx+1)), ', 间隔: ', num2str(timeIntervals(idx)), ' 秒']);
                    end
                end
            end
        end

        function plotGnssTimeIntervals(obj, details)
            % 绘制GNSS周内秒数据的时间间隔图
            % 输入:
            %   details - 由checkGnssTimeInterval函数返回的详细信息结构体

            % 如果没有提供details参数，则先调用checkGnssTimeInterval函数
            if nargin < 2
                [~, details] = obj.checkGnssTimeInterval();
            end

            % 创建新的图形窗口
            figure('Name', 'GNSS周内秒数据时间间隔分析', 'NumberTitle', 'off', 'Position', [100, 100, 1200, 800]);

            % 创建子图1：时间间隔随时间的变化
            subplot(2, 2, 1);
            plot(details.gnssTimeData(1:end-1), details.actualIntervals, 'b-', 'LineWidth', 1);
            hold on;

            % 标记所有异常点
            if ~isempty(details.anomalies.indices)
                plot(details.gnssTimeData(details.anomalies.indices), details.anomalies.values, 'ro', 'MarkerSize', 6, 'LineWidth', 2);
            end

            % 特别标记丢帧点
            if isfield(details, 'droppedFrames') && ~isempty(details.droppedFrames.locations)
                plot(details.gnssTimeData(details.droppedFrames.locations), ...
                     details.actualIntervals(details.droppedFrames.locations), ...
                     'mo', 'MarkerSize', 8, 'LineWidth', 2);
            end

            % 绘制预期间隔的水平线
            plot([details.gnssTimeData(1), details.gnssTimeData(end-1)], [details.expectedInterval, details.expectedInterval], 'g--', 'LineWidth', 2);
            hold off;
            title('GNSS周内秒时间间隔随时间的变化');
            xlabel('GNSS周内秒');
            ylabel('时间间隔 (秒)');
            grid on;

            % 根据是否有丢帧调整图例
            if isfield(details, 'droppedFrames') && ~isempty(details.droppedFrames.locations)
                legend('实际间隔', '异常点', '丢帧点', '预期间隔');
            else
                legend('实际间隔', '异常点', '预期间隔');
            end

            % 创建子图2：时间间隔的直方图
            subplot(2, 2, 2);
            histogram(details.actualIntervals, min(100, length(unique(details.actualIntervals))));
            hold on;
            % 绘制预期间隔的垂直线
            yLimits = ylim;
            plot([details.expectedInterval, details.expectedInterval], [0, yLimits(2)], 'r--', 'LineWidth', 2);
            hold off;
            title('时间间隔分布直方图');
            xlabel('时间间隔 (秒)');
            ylabel('频数');
            grid on;

            % 创建子图3：GNSS周内秒随索引的变化
            subplot(2, 2, 3);
            plot(1:length(details.gnssTimeData), details.gnssTimeData, 'b-', 'LineWidth', 1);
            hold on;

            % 在GNSS周内秒图上标记丢帧位置
            if isfield(details, 'droppedFrames') && ~isempty(details.droppedFrames.locations)
                % 在丢帧位置绘制垂直线
                for i = 1:length(details.droppedFrames.locations)
                    idx = details.droppedFrames.locations(i);
                    plot([idx, idx], [min(details.gnssTimeData), max(details.gnssTimeData)], 'm--', 'LineWidth', 1);
                end
            end

            hold off;
            title('GNSS周内秒随索引的变化（垂直线表示丢帧位置）');
            xlabel('数据点索引');
            ylabel('GNSS周内秒');
            grid on;

            % 创建子图4：丢帧分布图或时间间隔的箱线图
            subplot(2, 2, 4);

            % 如果有丢帧，绘制丢帧分布图
            if isfield(details, 'droppedFrames') && details.droppedFrames.count > 0
                % 创建丢帧数量的饼图
                pie([details.droppedFrames.count, length(details.actualIntervals) + 1 - details.droppedFrames.count]);
                title('数据完整性分析');
                legend({'丢失帧', '有效帧'}, 'Location', 'southoutside');

                % 添加丢帧信息文本
                text(0, 1.2, sprintf('总丢帧数: %d', details.droppedFrames.count), ...
                    'HorizontalAlignment', 'center', 'FontWeight', 'bold');
                text(0, 1.1, sprintf('丢帧发生次数: %d', length(details.droppedFrames.locations)), ...
                    'HorizontalAlignment', 'center');

                % 如果丢帧次数不多，显示每次丢帧的帧数
                if length(details.droppedFrames.locations) <= 5
                    for i = 1:length(details.droppedFrames.locations)
                        text(0, 1.0 - i*0.1, sprintf('位置 %d: 丢失 %d 帧', ...
                            details.droppedFrames.locations(i), details.droppedFrames.framesPerDrop(i)), ...
                            'HorizontalAlignment', 'center');
                    end
                end
            else
                % 如果没有丢帧，绘制时间间隔的箱线图
                boxplot(details.actualIntervals);
                title('时间间隔箱线图');
                ylabel('时间间隔 (秒)');
                grid on;
            end

            % 添加总体信息文本框
            if isfield(details, 'droppedFrames')
                annotation('textbox', [0.1, 0.01, 0.8, 0.05], 'String', ...
                    sprintf(['统计信息: 最小值=%.6f, 最大值=%.6f, 平均值=%.6f, 标准差=%.6f\n', ...
                             '预期间隔: %.6f, 异常点: %d (%.2f%%), 丢帧数: %d, 丢帧率: %.2f%%'], ...
                             details.statistics.min, details.statistics.max, ...
                             details.statistics.mean, details.statistics.std, ...
                             details.expectedInterval, length(details.anomalies.indices), ...
                             100 * length(details.anomalies.indices) / length(details.actualIntervals), ...
                             details.droppedFrames.count, ...
                             100 * details.droppedFrames.count / (length(details.actualIntervals) + details.droppedFrames.count)), ...
                    'EdgeColor', 'none', 'HorizontalAlignment', 'center');
            else
                annotation('textbox', [0.1, 0.01, 0.8, 0.05], 'String', ...
                    sprintf(['统计信息: 最小值=%.6f, 最大值=%.6f, 平均值=%.6f, 标准差=%.6f\n', ...
                             '预期间隔: %.6f, 异常点数量: %d (%.2f%%)'], ...
                             details.statistics.min, details.statistics.max, ...
                             details.statistics.mean, details.statistics.std, ...
                             details.expectedInterval, length(details.anomalies.indices), ...
                             100 * length(details.anomalies.indices) / length(details.actualIntervals)), ...
                    'EdgeColor', 'none', 'HorizontalAlignment', 'center');
            end
        end

        function obj = readCSV(obj)
            % 读取CSV文件
            % 这里假设CSV文件的每一行代表一个帧的数据
            % 使用readmatrix读取数据
            data = readmatrix(obj.csvFilePath);

            % 将数据存储到rawDataFrames属性中
                % obj.rawDataFrames.dGpsTime = data(:,20);
            obj.rawDataFrames = data(:,[18,5:10]);
            obj.numFrames = size(data, 1); % 获取帧的数量

            gyro_colunm = obj.rawDataFrames(:,2:4);
            acc_colunm  = obj.rawDataFrames(:,5:7);

            %%%
            obj.rawDataFrames(:,2:4) =  gyro_colunm;
            obj.rawDataFrames(:,5:7) =  acc_colunm .* obj.gn;

            % 2025-4-23 前数据需进行正负号修正
            % obj.rawDataFrames(:,2) = -1.0 .* obj.rawDataFrames(:,2);
            % obj.rawDataFrames(:,3) = -1.0 .* obj.rawDataFrames(:,3);
            % obj.rawDataFrames(:,5) = -1.0 .* obj.rawDataFrames(:,5);

            % 若IMU型号为INS370-25J，则将前上右顺序调整为右前上
            gyro = obj.rawDataFrames(:,2:4);
            accel = obj.rawDataFrames(:,5:7);
            [gyro, accel] = obj.adjustImuAxisOrder(gyro, accel);
            obj.rawDataFrames(:,2:4) = gyro;
            obj.rawDataFrames(:,5:7) = accel;

            disp(['读取CSV文件完成，共读取到 ', num2str(obj.numFrames), ' 帧数据']);
        end

        function obj = generateFrameHeader(obj, dDataRateHz, dGyroScaleFactor, dAccelScaleFactor)
            % 生成新的帧头
            % 输入:
            %   bIsIntelOrMotorola - 0 = Intel (Little Endian), 1 = Motorola (Big Endian)
            %   dVersionNumber - Inertial Explorer 程序版本号（例如 8.80）
            %   bDeltaTheta - 0 = 数据为角速率, 1 = 数据为角增量（默认）
            %   bDeltaVelocity - 0 = 数据为加速度, 1 = 数据为速度增量（默认）
            %   dDataRateHz - IMU 数据速率（Hz）
            %   dGyroScaleFactor - 陀螺仪比例因子
            %   dAccelScaleFactor - 加速度计比例因子
            %   iUtcOrGpsTime - 时间标签类型: 0 = 未知, 1 = UTC, 2 = GPS
            %   iRcvTimeOrCorrTime - 时间标签类型: 0 = 未知, 1 = 标准时间, 2 = 校正时间
            %   dTimeTagBias - GPS 和 IMU 时间标签之间的已知偏差
            %   szImuName - IMU 名称（最多 32 字符）
            %   szProgramName - 调用程序名称（最多 32 字符）
            %   tCreate - 文件创建时间（12 字节）
            %   bLeverArmValid - 是否包含杠杆臂信息
            %   lXoffset - 杠杆臂 X 值（毫米）
            %   lYoffset - 杠杆臂 Y 值（毫米）
            %   lZoffset - 杠杆臂 Z 值（毫米）
            % 输出:
            %   obj - 包含生成的帧头的对象

            iRcvTimeOrCorrTime = 1; % 标准时间
            dTimeTagBias = 0.0; % 时间标签偏差
            bLeverArmValid = true; % 杆臂有效
            lXoffset = -4.54;      %-4.54; % 杠杆臂 X 值（毫米） 右
            lYoffset = -748.4;    %-748.4; % 杠杆臂 Y 值（毫米） 前
            lZoffset = -50.50;     %-50.5; % 杠杆臂 Z 值（毫米） 上

            % 初始化帧头为 512 字节的零数组
            obj.frameHeader = zeros(1, 512, 'uint8');

            % 1. szHeader (8字节): "$IMURAW\0" – NULL 终止的 ASCII 字符串
            headerStr = '$IMURAW';
            obj.frameHeader(1:8) = [uint8(headerStr), 0]; % NULL 终止

            % 2. bIsIntelOrMotorola (1字节): int8_t 类型
            obj.frameHeader(9) = uint8(obj.bIsIntelOrMotorola);

            % 3. dVersionNumber (8字节): double 类型
            obj.frameHeader(10:17) = typecast(obj.dVersionNumber, 'uint8');

            % 4. bDeltaTheta (4字节): int32_t 类型
            obj.frameHeader(18:21) = typecast(int32(obj.bDeltaTheta), 'uint8');

            % 5. bDeltaVelocity (4字节): int32_t 类型
            obj.frameHeader(22:25) = typecast(int32(obj.bDeltaVelocity), 'uint8');

            % 6. dDataRateHz (8字节): double 类型
            obj.frameHeader(26:33) = typecast(dDataRateHz, 'uint8');

            % 7. dGyroScaleFactor (8字节): double 类型
            obj.frameHeader(34:41) = typecast(dGyroScaleFactor, 'uint8');

            % 8. dAccelScaleFactor (8字节): double 类型
            obj.frameHeader(42:49) = typecast(dAccelScaleFactor, 'uint8');

            % 9. iUtcOrGpsTime (4字节): int32_t 类型
            obj.frameHeader(50:53) = typecast(int32(obj.iUtcOrGpsTime), 'uint8');

            % 10. iRcvTimeOrCorrTime (4字节): int32_t 类型
            obj.frameHeader(54:57) = typecast(int32(iRcvTimeOrCorrTime), 'uint8');

            % 11. dTimeTagBias (8字节): double 类型
            obj.frameHeader(58:65) = typecast(dTimeTagBias, 'uint8');

            % 12. szImuName (32字节): char[32] 类型
            imuNameBytes = uint8(char(obj.szImuName));
            if length(imuNameBytes) < 32
                imuNameBytes = [imuNameBytes, zeros(1, 32 - length(imuNameBytes), 'uint8')]; % 填充空字符
            end
            obj.frameHeader(66:97) = imuNameBytes(1:32); % 确保不超过 32 字节

            % 13. reserved1 (4字节): uint8_t[4] 类型，保留字段
            obj.frameHeader(98:101) = 0; % 填充 0

            % 14. szProgramName (32字节): char[32] 类型
            programNameBytes = uint8(char(obj.szProgramName));
            if length(programNameBytes) < 32
                programNameBytes = [programNameBytes, zeros(1, 32 - length(programNameBytes), 'uint8')]; % 填充空字符
            end
            obj.frameHeader(102:133) = programNameBytes(1:32); % 确保不超过 32 字节

            % 15. tCreate (12字节): time_type 类型
            % 将 obj.csvFileCreationTime 转换为 uint8 字节数组
            timeBytes = typecast(obj.csvFileCreationTime, 'uint8'); % 8 字节
            % 填充 4 字节的 0，使总长度为 12 字节
            timeBytes = [timeBytes, zeros(1, 4, 'uint8')]; % 填充 4 字节的 0
            % 将 12 字节的时间数据写入帧头
            obj.frameHeader(134:145) = timeBytes;

            % 16. bLeverArmValid (1字节): bool 类型
            obj.frameHeader(146) = uint8(bLeverArmValid);

            % 17. lXoffset (4字节): int32_t 类型
            obj.frameHeader(147:150) = typecast(int32(lXoffset), 'uint8');

            % 18. lYoffset (4字节): int32_t 类型
            obj.frameHeader(151:154) = typecast(int32(lYoffset), 'uint8');

            % 19. lZoffset (4字节): int32_t 类型
            obj.frameHeader(155:158) = typecast(int32(lZoffset), 'uint8');

            % 20. Reserved[354] (354字节): int8_t[354] 类型，保留字段
            obj.frameHeader(159:512) = 0; % 填充 0

            disp('生成帧头完成');
        end

        function obj = generateDataFrames(obj)
            % 生成所有数据帧
            % 输入:
            %   ucType - 1 = 外部源, 2 = 双天线源
            disp('生成数据帧...');

            for i = 1:obj.numFrames
                frameData = obj.rawDataFrames(i,:); % 获取第i帧的数据

                % 根据协议处理帧数据
                processedFrame = obj.processFrameData(frameData); % 处理帧数据
                obj.dataFrames(i, :) = processedFrame; % 更新数据帧

                % 计算并显示读取进度
                if mod(i,1e4)==0
                    progress = i / obj.numFrames * 100; % 计算进度百分比
                    fprintf('读取进度: %.2f%%\n', progress); % 显示进度
                end
            end
            disp('数据帧生成完成');
        end

        function processedFrame = processFrameData(obj, frameData)
            % 处理单个帧的数据
            % 输入:
            %   frameData - 原始帧数据（从CSV文件中读取的一行）
            % 输出:
            %   processedFrame - 处理后的帧数据（符合协议要求）

            % 1. Time (8字节): double类型
            dTime = frameData(1); % 假设CSV文件的第1列是Time
            dTimeBytes = typecast(dTime, 'uint8');

            % 2. gx (4字节): int32_t类型
            gx_ = frameData(2) / obj.dGyroScaleFactor;
            gx = int32(gx_); % 假设CSV文件的第2列是gx
            gxBytes = typecast(gx, 'uint8');

            % 3. gy (4字节): int32_t类型
            gy_ = frameData(3) / obj.dGyroScaleFactor; % 乘以陀螺仪比例因子
            gy = int32(gy_); % 转换为 int32
            gyBytes = typecast(gy, 'uint8'); % 转换为 uint8 字节数组

            % 4. gz (4字节): int32_t类型
            gz_ = frameData(4) / obj.dGyroScaleFactor; % 乘以陀螺仪比例因子
            gz = int32(gz_); % 转换为 int32
            gzBytes = typecast(gz, 'uint8'); % 转换为 uint8 字节数组

            % 5. ax (4字节): int32_t类型
            ax_ = frameData(5) / obj.dAccelScaleFactor; % 乘以加速度计比例因子
            ax = int32(ax_); % 转换为 int32
            axBytes = typecast(ax, 'uint8'); % 转换为 uint8 字节数组

            % 6. ay (4字节): int32_t类型
            ay_ = frameData(6) / obj.dAccelScaleFactor; % 乘以加速度计比例因子
            ay = int32(ay_); % 转换为 int32
            ayBytes = typecast(ay, 'uint8'); % 转换为 uint8 字节数组

            % 7. az (4字节): int32_t类型
            az_ = frameData(7) / obj.dAccelScaleFactor; % 乘以加速度计比例因子
            az = int32(az_); % 转换为 int32
            azBytes = typecast(az, 'uint8'); % 转换为 uint8 字节数组

            % 将处理后的数据帧拼接为一个字节数组
            processedFrame = [dTimeBytes, gxBytes, gyBytes, gzBytes, axBytes, ayBytes, azBytes];
        end

        function obj = writeIMRFile(obj)
            % 将帧头和数据帧写入IMR文件
            % 打开文件
            fileID = fopen(obj.imrFilePath, 'wb'); % 以二进制写入模式打开文件
            if fileID == -1
                error('无法打开IMR文件');
            end

            % 写入帧头
            fwrite(fileID, obj.frameHeader, 'uint8');

            % 写入所有数据帧（每个帧前添加帧头）
            for i = 1:length(obj.dataFrames(:,1))
                % 写入数据帧
                frameData = obj.dataFrames(i, :); % 获取第i帧的数据
                fwrite(fileID, frameData, 'uint8'); % 写入帧数据
            end

            % 关闭文件
            fclose(fileID);
            disp(['IMR文件已写入：', obj.imrFilePath]);
        end

        function obj = generateIMR(obj, showPlot)
            % 生成IMR文件的完整流程
            % 输入:
            %   showPlot - 是否显示GNSS周内秒数据的可视化图表（默认为false）

            % 处理可选参数
            if nargin < 2
                showPlot = false;
            end

            if isempty(obj.rawDataFrames) || obj.numFrames == 0
                error('数据未初始化，请通过构造函数初始化对象');
            end

            % 检查GNSS周内秒数据是否按照固定间隔增加
            [isValid, details] = obj.checkGnssTimeInterval();

            % 如果需要，显示可视化图表
            if showPlot || ~isValid
                obj.plotGnssTimeIntervals(details);
            end

            if ~isValid
                warning('GNSS周内秒数据存在异常，可能会影响IMR文件的质量');
                % 可以根据需要决定是否继续生成IMR文件
                % 如果异常点太多，可以考虑中止处理
                if length(details.anomalies.indices) > obj.numFrames * 0.1 % 如果异常点超过10%
                    error('GNSS周内秒数据异常点过多，中止IMR文件生成');
                end
            end

            dDataRateHz = 200;
            obj = obj.generateFrameHeader(dDataRateHz,obj.dGyroScaleFactor,obj.dAccelScaleFactor);
            obj = obj.generateDataFrames();
            obj.writeIMRFile();
            disp('IMR文件生成完成！');
        end

        function [gyroAdj, accelAdj] = adjustImuAxisOrder(obj, gyro, accel)
            % adjustImuAxisOrder - 若IMU型号为INS370-25J，则将gyro和accel从前上右调整为右前上
            if isprop(obj, 'szImuName') && strcmp(obj.szImuName, 'INS370-25J')
                gyroAdj = gyro(:, [3, 1, 2]);
                accelAdj = accel(:, [3, 1, 2]);
            else
                gyroAdj = gyro;
                accelAdj = accel;
            end
        end
    end
end
